package org.jeecg.modules.system.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.config.TenantContext;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.constant.SymbolConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.TokenUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.mybatis.MybatisPlusSaasConfig;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.monitor.actuator.httptrace.CustomInMemoryHttpTraceRepository;
import org.jeecg.modules.system.dto.*;
import org.jeecg.modules.system.entity.*;
import org.jeecg.modules.system.mapper.SysTenantMapper;
import org.jeecg.modules.system.service.*;
import org.jeecg.modules.system.service.ITenantCityRelationService;
import org.jeecg.modules.system.service.ITenantBusinessTypeRelationService;
import org.jeecg.modules.system.vo.SysPdTenantVO;
import org.jeecg.modules.system.vo.SysUserTenantVo;
import org.jeecg.modules.system.vo.tenant.*;
import org.jeecg.modules.system.vo.tenant.TenantDetailsListVO;
import org.jeecg.modules.wh.service.IWhRecordStatisticsService;
import org.jeecg.modules.wh.vo.WhRecordStatisticsVO;
import org.springframework.beans.BeanUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户配置信息
 * @author: jeecg-boot
 */
@Slf4j
@RestController
    @RequestMapping("/sys/tenant")
public class SysTenantController {

    @Autowired
    private ISysTenantService sysTenantService;

    @Resource
    private SysTenantMapper baseMapper;

    @Resource
    private ISysRoleService sysRoleService;


    @Resource
    private ISysUserRoleService sysUserRoleService;

    @Autowired
    private ISysUserService sysUserService;


    @Autowired
    private ISysUserTenantService relationService;

    @Autowired
    private ISysTenantPackService sysTenantPackService;

    @Autowired
    private BaseCommonService baseCommonService;

    @Autowired
    private ISysDepartService sysDepartService;
    @Resource
    private ISysDictService sysDictService;

    @Autowired
    private IWhRecordStatisticsService whRecordStatisticsService;
    @Resource
    private ISysDictItemService sysDictItemService;

    @Autowired
    private ITenantCityRelationService tenantCityRelationService;

    @Autowired
    private ITenantBusinessTypeRelationService tenantBusinessTypeRelationService;
    @Autowired
    private CustomInMemoryHttpTraceRepository traceRepository;

    /**
     * 获取列表数据
     * @param sysTenant
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @RequiresPermissions("system:tenant:list")
    @PermissionData(pageComponent = "system/TenantList")
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public Result<IPage<SysTenant>> queryPageList(SysTenantDto dto, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		Result<IPage<SysTenant>> result = new Result<IPage<SysTenant>>();

		Page<SysTenant> page = new Page<SysTenant>(pageNo, pageSize);
		IPage<SysTenant> pageList = sysTenantService.queryPageList(page, dto);
		result.setSuccess(true);
		result.setResult(pageList);
		return result;
	}

    @RequestMapping(value = "/queryListPortal", method = RequestMethod.GET)
    public Result<IPage<SysTenantPortalVO>> queryListPortal(SysAddTenantUserDto dto, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                      @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {

        //---author:zhangyafei---date:20210916-----for: 租户管理添加日期范围查询---
        Page<SysTenantPortalVO> page = new Page<SysTenantPortalVO>(pageNo, pageSize);
        IPage<SysTenantPortalVO> pageList = sysTenantService.queryList(page, dto);
        return Result.ok(pageList);
    }

    @RequestMapping(value = "/queryAllList", method = RequestMethod.POST)
    @ApiOperation(value="租户列表", notes="租户列表-全量查询")
    public Result<List<SysPdTenantVO>> queryAllList(@RequestBody SysTenantUserDto dto) {
        List<SysPdTenantVO> pageList = sysTenantService.queryAllList(dto);
        return Result.ok(pageList);
    }

    /**
     * 获取合作公司目录（只返回id和name以及序号）
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param name 公司名称（可选，用于搜索）
     * @return 分页结果
     */
    @RequestMapping(value = "/getCompanyDirectory", method = RequestMethod.GET)
    public Result<IPage<SysTenantVO>> getCompanyDirectory(
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
            SysTenantDto dto) {

        Result<IPage<SysTenantVO>> result = new Result<>();
        Page<SysTenantVO> page = new Page<>(pageNo, pageSize);

        // 使用XML中的SQL查询，包含序号
        IPage<SysTenantVO> pageList = sysTenantService.getCompanyDirectory(page, dto);

        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    @ApiOperation(value="获取公司详情")
    @GetMapping(value = "/getByComName")
    public Result<SysTenant> getByComName(@RequestParam(required = true) String comName) {
        SysTenant sysTenant = sysTenantService.lambdaQuery().eq(SysTenant::getName, comName).last(" limit 1").one();
        if (sysTenant == null) {
            return Result.error("未找到对应数据");
        }
        sysTenant.setClickNum(sysTenantService.getNameClick(comName));
        return Result.OK(sysTenant);
    }

    @ApiOperation(value="获取公司详情")
    @GetMapping(value = "/getById")
    public Result<SysTenant> getById(@RequestParam(required = true) String id) {
        SysTenant sysTenant = sysTenantService.getById(id);
        if (sysTenant == null) {
            return Result.error("未找到对应数据");
        }
        String click = sysTenantService.getNameClick(String.valueOf(sysTenant.getId()));
        if (StrUtil.isNotEmpty(click)) {
            click = String.valueOf(Integer.parseInt(click) );
            sysTenant.setClickNum(click);
        }

        // 查询统计信息
        try {
            WhRecordStatisticsVO statisticsVO = whRecordStatisticsService.getStatisticsByPid(id);
            if (statisticsVO != null) {
                sysTenant.setBrowseCount(statisticsVO.getBrowseCount());
                sysTenant.setFavoriteCount(statisticsVO.getFavoriteCount());
                sysTenant.setLikeCount(statisticsVO.getLikeCount());
                sysTenant.setShareCount(statisticsVO.getShareCount());
            } else {
                // 如果没有统计数据，设置为0
                sysTenant.setBrowseCount(0);
                sysTenant.setFavoriteCount(0);
                sysTenant.setLikeCount(0);
                sysTenant.setShareCount(0);
            }
        } catch (Exception e) {
            log.error("查询统计信息失败，租户ID：{}", id, e);
            // 出现异常时设置为0
            sysTenant.setBrowseCount(0);
            sysTenant.setFavoriteCount(0);
            sysTenant.setLikeCount(0);
            sysTenant.setShareCount(0);
        }

        return Result.OK(sysTenant);
    }

    @GetMapping("/getTenantList")
    public Result<List<SysTenantVO>> getTenantList(@RequestParam(required = false) String tenantType) {
        // 初始化空结果
        List<SysTenantVO> resultList = new ArrayList<>();

        LambdaQueryWrapper<SysTenant> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.select(SysTenant::getId, SysTenant::getName);
        if (StrUtil.isNotEmpty(tenantType)) {
            String[] split = tenantType.split(",");
            queryWrapper.in(SysTenant::getTenantType, split);
        }
        List<SysTenant> tenantList = sysTenantService.list(queryWrapper);
        if (tenantList != null && !tenantList.isEmpty()) {
            resultList = tenantList.stream()
                    .filter(Objects::nonNull)
                    .map(t -> {
                        SysTenantVO vo = new SysTenantVO();
                        vo.setId(t.getId());
                        vo.setName(t.getName());
                        return vo;
                    })
                    .collect(Collectors.toList());
        }

        return Result.OK(resultList);
    }

    @GetMapping("/getTenantDetailsList")
    public Result<TenantDetailsListVO> getTenantDetailsList(
            @RequestParam(required = false) String tenantType,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String tenantName,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="15") Integer pageSize) {

        // 还原原有的推荐供应商逻辑，这个不走配置
        TenantDetailsListVO result = sysTenantService.getTenantDetailsWithRecommendAndPage(tenantType, city, tenantName, pageNo, pageSize);
        return Result.OK(result);
    }





    @GetMapping("/getByTentId")
    public Result<SysTenantResVO> getByTentId(@RequestParam(required = false)String id) {
        id = "0";
        SysTenant sysTenant = null;
        if (StrUtil.isNotEmpty(id)) {
            sysTenant = sysTenantService.getById(id);
        }else {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String tenantId = request.getHeader("tenant-id-link");
            tenantId = "0";
            if (StrUtil.isEmpty(tenantId)) {
                throw new RuntimeException("未找到租户 id");
            }
            sysTenant = sysTenantService.getById(tenantId);
        }
        SysTenantResVO resultList = new SysTenantResVO();
        if (sysTenant != null) {
            resultList.setId(sysTenant.getId())
                    .setName(sysTenant.getName())
                    .setCompanyAddress(sysTenant.getCompanyAddress())
                    .setIntro(sysTenant.getIntro()).setLegalPerson(sysTenant.getLegalPerson())
                    .setRegTime(sysTenant.getRegTime());
        }

        return Result.OK(resultList);
    }

    /**
     * 获取租户删除的列表
     * @param sysTenant
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping("/recycleBinPageList")
    @RequiresPermissions("system:tenant:recycleBinPageList")
    public Result<IPage<SysTenant>> recycleBinPageList(SysTenant sysTenant,@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,HttpServletRequest req){
        Result<IPage<SysTenant>> result = new Result<IPage<SysTenant>>();
        Page<SysTenant> page = new Page<SysTenant>(pageNo, pageSize);
        IPage<SysTenant> pageList = sysTenantService.getRecycleBinPageList(page, sysTenant);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     *   添加
     * @param
     * @return
     */
    @RequiresPermissions("system:tenant:add")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @CacheEvict(value = "tenant_list", allEntries=true)
    public Result<SysTenant> add(@RequestBody SysTenant sysTenant) {
        Result<SysTenant> result = new Result();
        if(sysTenantService.getById(sysTenant.getId())!=null){
            return result.error500("该编号已存在!");
        }
        try {


            // 保存租户信息
            sysTenantService.saveTenant(sysTenant);

            // 处理点击量
            String id = UUID.randomUUID().toString().replaceAll("-", "");
            Integer clicksNum = sysTenant.getClickNum() != null ? Integer.parseInt(sysTenant.getClickNum()) : 0;
            // 保存点击量记录，获取点击ID
            this.baseMapper.saveWhClick(clicksNum, String.valueOf(sysTenant.getId()), id);

            // 更新租户与点击量的关联

            //添加默认产品包
            sysTenantPackService.addTenantDefaultPack(sysTenant.getId());
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    @RequestMapping(value = "/addTenantUser", method = RequestMethod.POST)
    @CacheEvict(value = "tenant_list", allEntries=true)
    public Result<?> addTenantUser(HttpServletRequest request) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<SysAddTenantUserDto> list = ExcelImportUtil.importExcel(file.getInputStream(), SysAddTenantUserDto.class, params);
                // 检查租户名称 tenantName 和租户编号 tenantCode 是否存在字段，否则报错，使用流
                list.forEach(item -> {
                    if (item.getTenantName() == null || item.getTenantName().trim().length() == 0) {
                        throw new RuntimeException("请填写租户名称");
                    }
                    if (item.getTenantCode() == 0) {  // 检查 tenantCode 是否为 0
                        throw new RuntimeException("请填写租户编号");
                    }
                });
                // 批量处理导入的租户用户数据 - 优化为批量操作
                int successCount = processTenantUserDataBatch(list);

                return Result.ok("文件导入成功！成功导入 " + successCount + " 条数据，总数据行数：" + list.size());
            } catch (Exception e) {
                String msg = e.getMessage();
                log.error(msg, e);
                if(msg!=null && msg.indexOf("Duplicate entry")>=0){
                    return Result.error("文件导入失败:有重复数据！");
                }else{
                    return Result.error("文件导入失败:" + e.getMessage());
                }
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /**
     *  编辑
     * @param
     * @return
     */
    @RequiresPermissions("system:tenant:edit")
    @RequestMapping(value = "/edit", method ={RequestMethod.PUT, RequestMethod.POST})
    @CacheEvict(value = "tenant_list", allEntries=true)
    public Result<SysTenant> edit(@RequestBody SysTenant tenant) {
        Result<SysTenant> result = new Result();
        SysTenant sysTenant = sysTenantService.getById(tenant.getId());
        if(sysTenant==null) {
           return result.error500("未找到对应实体");
        }
        if(oConvertUtils.isEmpty(sysTenant.getHouseNumber())){
            tenant.setHouseNumber(RandomUtil.randomStringUpper(6));
        }

        try {
            // 处理点击量
            if (tenant.getClickNum() != null) {
                Integer clicksNum = Integer.parseInt(tenant.getClickNum());

                // 查询当前租户关联的点击ID
                String clicksId = this.baseMapper.getClicksIdByCompanyId(tenant.getId());

                if (clicksId != null && !clicksId.isEmpty()) {
                    // 如果已有点击记录，则更新
                    this.baseMapper.updateWhClick(clicksNum, String.valueOf(tenant.getId()));
                } else {
                    String id = UUID.randomUUID().toString().replaceAll("-", ""); // 去掉短横线，生成32位字符串ID
                    this.baseMapper.saveWhClick( clicksNum, String.valueOf(sysTenant.getId()),id);
                }
            }

            // 更新租户信息
            boolean ok = sysTenantService.updateById(tenant);
            if(ok) {
                result.success("修改成功!");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }

        return result;
    }

    /**
     *   通过id删除
     * @param id
     * @return
     */
    @RequiresPermissions("system:tenant:delete")
    @RequestMapping(value = "/delete", method ={RequestMethod.DELETE, RequestMethod.POST})
    @CacheEvict(value = "tenant_list", allEntries=true)
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        //------------------------------------------------------------------
        //如果是saas隔离的情况下，判断当前租户id是否是当前租户下的
        if (MybatisPlusSaasConfig.OPEN_SYSTEM_TENANT_CONTROL) {
            //获取当前用户
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            SysTenant sysTenant = sysTenantService.getById(id);

            String username = "admin";
            String createdBy = sysUser.getUsername();
            if (!sysTenant.getCreateBy().equals(createdBy) && !username.equals(createdBy)) {
                baseCommonService.addLog("未经授权，不能删除非自己创建的租户，租户ID：" + id + "，操作人：" + sysUser.getUsername(), CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_3);
                return Result.error("删除租户失败,当前操作人不是租户的创建人！");
            }
        }
        //------------------------------------------------------------------

        sysTenantService.removeTenantById(id);
        return Result.ok("删除成功");
    }

    /**
     *  批量删除
     * @param ids
     * @return
     */
    @RequiresPermissions("system:tenant:deleteBatch")
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.DELETE)
    @CacheEvict(value = "tenant_list", allEntries=true)
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<?> result = new Result<>();
        if(oConvertUtils.isEmpty(ids)) {
            result.error500("未选中租户！");
        }else {
            String[] ls = ids.split(",");
            // 过滤掉已被引用的租户
            List<Integer> idList = new ArrayList<>();
            for (String id : ls) {
                //------------------------------------------------------------------
                //如果是saas隔离的情况下，判断当前租户id是否是当前租户下的
//                if (MybatisPlusSaasConfig.OPEN_SYSTEM_TENANT_CONTROL) {
//                    //获取当前用户
//                    LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
//                    SysTenant sysTenant = sysTenantService.getById(id);
//
//                    String username = "admin";
//                    String createdBy = sysUser.getUsername();
//                    if (!sysTenant.getCreateBy().equals(createdBy) && !username.equals(createdBy)) {
//                        baseCommonService.addLog("未经授权，不能删除非自己创建的租户，租户ID：" + id + "，操作人：" + sysUser.getUsername(), CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_3);
//                        return Result.error("删除租户失败,当前操作人不是租户的创建人！");
//                    }
//                }
                //------------------------------------------------------------------

                idList.add(Integer.parseInt(id));
            }
            //update-begin---author:wangshuai ---date:20230710  for：【QQYUN-5723】3、租户删除直接删除，不删除中间表------------
            sysTenantService.removeByIdList(idList);
            result.success("删除成功！");
            //update-end---author:wangshuai ---date:20220523  for：【QQYUN-5723】3、租户删除直接删除，不删除中间表------------
        }
        return result;
    }

    /**
     * 通过id查询
     * @param id
     * @return
     */
    @RequestMapping(value = "/queryById", method = RequestMethod.GET)
    public Result<SysTenant> queryById(@RequestParam(name="id",required=true) String id) {
        Result<SysTenant> result = new Result<SysTenant>();
        if(oConvertUtils.isEmpty(id)){
            result.error500("参数为空！");
        }
        //------------------------------------------------------------------------------------------------
        //获取登录用户信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //是否开启系统管理模块的多租户数据隔离【SAAS多租户模式】, admin给特权可以管理所有租户
        if(MybatisPlusSaasConfig.OPEN_SYSTEM_TENANT_CONTROL && !"admin".equals(sysUser.getUsername())){
            Integer loginSessionTenant = oConvertUtils.getInt(TenantContext.getTenant());
            if ((loginSessionTenant != null && loginSessionTenant == 0)) {

            }else if((loginSessionTenant!=null && !loginSessionTenant.equals(Integer.valueOf(id)))){
                result.error500("无权限访问他人租户！");
                return result;
            }
        }
        //------------------------------------------------------------------------------------------------
        SysTenant sysTenant = sysTenantService.getById(id);
        if(sysTenant==null) {
            result.error500("未找到对应实体");
        }else {
            // 查询当前租户关联的点击ID
            String clickNum = this.baseMapper.getClicksIdByCompanyId(sysTenant.getId());

            if (StrUtil.isNotEmpty(clickNum)) {
                sysTenant.setClickNum(clickNum);
            }
            result.setResult(sysTenant);
            result.setSuccess(true);
        }
        return result;
    }


    /**
     * 导出excel
     *
     * @param request
     * @param sysTenant
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("system:tenant:list")
    public ModelAndView exportXls(HttpServletRequest request, SysTenant sysTenant) {
        // Step.1 组装查询条件
        QueryWrapper<SysTenant> queryWrapper = QueryGenerator.initQueryWrapper(sysTenant, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id", selectionList);
        }

        // Step.2 获取导出数据
        List<SysTenant> tenantList = sysTenantService.list(queryWrapper);

        // Step.3 转换为导出VO
        List<SysTenantExportVO> exportList = new ArrayList<>();
        for (SysTenant tenant : tenantList) {
            SysTenantExportVO exportVO = new SysTenantExportVO();
            BeanUtils.copyProperties(tenant, exportVO);
            exportList.add(exportVO);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "租户列表");
        mv.addObject(NormalExcelConstants.CLASS, SysTenantExportVO.class);
        ExportParams exportParams = new ExportParams("租户列表报表", "导出人:" + sysUser.getRealname(), "租户列表");
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 查询有效的 租户数据
     * @return
     */
    @RequiresPermissions("system:tenant:queryList")
    @RequestMapping(value = "/queryList", method = RequestMethod.GET)
    public Result<List<SysTenant>> queryList(@RequestParam(name="ids",required=false) String ids) {
        Result<List<SysTenant>> result = new Result<List<SysTenant>>();
        LambdaQueryWrapper<SysTenant> query = new LambdaQueryWrapper<>();
        query.eq(SysTenant::getStatus, 1);
        if(oConvertUtils.isNotEmpty(ids)){
            query.in(SysTenant::getId, ids.split(","));
        }
        //此处查询忽略时间条件
        List<SysTenant> ls = sysTenantService.list(query);
        result.setSuccess(true);
        result.setResult(ls);
        return result;
    }

    /**
     * 产品包分页列表查询
     *
     * @param sysTenantPack
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping(value = "/packList")
    @RequiresPermissions("system:tenant:packList")
    public Result<IPage<SysTenantPack>> queryPackPageList(SysTenantPack sysTenantPack,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          HttpServletRequest req) {
        QueryWrapper<SysTenantPack> queryWrapper = QueryGenerator.initQueryWrapper(sysTenantPack, req.getParameterMap());
        Page<SysTenantPack> page = new Page<SysTenantPack>(pageNo, pageSize);
        IPage<SysTenantPack> pageList = sysTenantPackService.page(page, queryWrapper);
        List<SysTenantPack> records = pageList.getRecords();
        if (null != records && records.size() > 0) {
            pageList.setRecords(sysTenantPackService.setPermissions(records));
        }
        return Result.OK(pageList);
    }

    /**
     * 创建租户产品包
     *
     * @param sysTenantPack
     * @return
     */
    @PostMapping(value = "/addPackPermission")
    @RequiresPermissions("system:tenant:add:pack")
    public Result<String> addPackPermission(@RequestBody SysTenantPack sysTenantPack) {
        sysTenantPackService.addPackPermission(sysTenantPack);
        return Result.ok("创建租户产品包成功");
    }

    /**
     * 创建租户产品包
     *
     * @param sysTenantPack
     * @return
     */
    @PutMapping(value = "/editPackPermission")
    @RequiresPermissions("system:tenant:edit:pack")
    public Result<String> editPackPermission(@RequestBody SysTenantPack sysTenantPack) {
        sysTenantPackService.editPackPermission(sysTenantPack);
        return Result.ok("修改租户产品包成功");
    }

    /**
     * 批量删除用户菜单
     *
     * @param ids
     * @return
     */
    @DeleteMapping("/deleteTenantPack")
    @RequiresPermissions("system:tenant:delete:pack")
    public Result<String> deleteTenantPack(@RequestParam(value = "ids") String ids) {
        sysTenantPackService.deleteTenantPack(ids);
        return Result.ok("删除租户产品包成功");
    }



    //===========【低代码应用，前端专用接口 —— 加入限制只能维护和查看自己拥有的租户】==========================================================
    /**
     *  查询当前用户的所有有效租户【低代码应用专用接口】
     * @return
     */
    @RequestMapping(value = "/getCurrentUserTenant", method = RequestMethod.GET)
    public Result<Map<String,Object>> getCurrentUserTenant() {
        Result<Map<String,Object>> result = new Result<Map<String,Object>>();
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            //update-begin---author:wangshuai ---date:20221223  for：[QQYUN-3371]租户逻辑改造，改成关系表------------
            List<Integer> tenantIdList = relationService.getTenantIdsByUserId(sysUser.getId());
            Map<String,Object> map = new HashMap(5);
            if (null!=tenantIdList && tenantIdList.size()>0) {
            //update-end---author:wangshuai ---date:20221223  for：[QQYUN-3371]租户逻辑改造，改成关系表------------
                // 该方法仅查询有效的租户，如果返回0个就说明所有的租户均无效。
                List<SysTenant> tenantList = sysTenantService.queryEffectiveTenant(tenantIdList);
                map.put("list", tenantList);
            }
            result.setSuccess(true);
            result.setResult(map);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            result.error500("查询失败！");
        }
        return result;
    }

    /**
     * 邀请用户【低代码应用专用接口】
     * @param ids
     * @param phone
     * @return
     */
    @PutMapping("/invitationUserJoin")
    @RequiresPermissions("system:tenant:invitation:user")
    public Result<String> invitationUserJoin(@RequestParam("ids") String ids,@RequestParam("phone") String phone){
        sysTenantService.invitationUserJoin(ids,phone);
        return Result.ok("邀请用户成功");
    }

    /**
     * 获取用户列表数据【低代码应用专用接口】
     * @param user
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @RequestMapping(value = "/getTenantUserList", method = RequestMethod.GET)
    @RequiresPermissions("system:tenant:user:list")
    public Result<IPage<SysUser>> getTenantUserList(SysUser user,
                                                    @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                    @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                    @RequestParam(name="userTenantId",required = false) String userTenantId,
                                                    HttpServletRequest req) {
        Result<IPage<SysUser>> result = new Result<>();
        Page<SysUser> page = new Page<>(pageNo, pageSize);
        Page<SysUser> pageList = relationService.getPageUserList(page,userTenantId,user);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 请离用户租户【低代码应用专用接口】
     * @param userIds
     * @param tenantId
     * @return
     */
    @PutMapping("/leaveTenant")
    @RequiresPermissions("system:tenant:leave")
    public Result<String> leaveTenant(@RequestParam("userIds") String userIds,
                                      @RequestParam("tenantId") String tenantId){
        Result<String> result = new Result<>();
        //是否开启系统管理模块的多租户数据隔离【SAAS多租户模式】
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if(MybatisPlusSaasConfig.OPEN_SYSTEM_TENANT_CONTROL && !"admin".equals(sysUser.getUsername())){
            Integer loginSessionTenant = oConvertUtils.getInt(TenantContext.getTenant());
            if(loginSessionTenant!=null && !loginSessionTenant.equals(Integer.valueOf(tenantId))){
                result.error500("无权限访问他人租户！");
                return result;
            }
        }
        sysTenantService.leaveTenant(userIds,tenantId);
        return Result.ok("请离成功");
    }

    /**
     *  编辑（只允许修改自己拥有的租户）【低代码应用专用接口】
     * @param
     * @return
     */
    @RequestMapping(value = "/editOwnTenant", method ={RequestMethod.PUT, RequestMethod.POST})
    public Result<SysTenant> editOwnTenant(@RequestBody SysTenant tenant,HttpServletRequest req) {
        Result<SysTenant> result = new Result();
        String tenantId = TokenUtils.getTenantIdByRequest(req);
        if(!tenantId.equals(tenant.getId().toString())){
            return result.error500("无权修改他人租户！");
        }

        SysTenant sysTenant = sysTenantService.getById(tenant.getId());
        if(sysTenant==null) {
            return result.error500("未找到对应实体");
        }
        if(oConvertUtils.isEmpty(sysTenant.getHouseNumber())){
            tenant.setHouseNumber(RandomUtil.randomStringUpper(6));
        }
        boolean ok = sysTenantService.updateById(tenant);
        if(ok) {
            result.success("修改成功!");
        }
        return result;
    }

    /**
     * 创建租户并且将用户保存到中间表【低代码应用专用接口】
     * @param sysTenant
     */
    @PostMapping("/saveTenantJoinUser")
    public Result<Integer> saveTenantJoinUser(@RequestBody SysTenant sysTenant){
        Result<Integer> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Integer tenantId = sysTenantService.saveTenantJoinUser(sysTenant, sysUser.getId());
        result.setSuccess(true);
        result.setMessage("创建成功");
        result.setResult(tenantId);
        return result;
    }

    /**
     * 加入租户通过门牌号【低代码应用专用接口】
     * @param sysTenant
     */
    @PostMapping("/joinTenantByHouseNumber")
    public Result<Integer> joinTenantByHouseNumber(@RequestBody SysTenant sysTenant){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Integer tenantId = sysTenantService.joinTenantByHouseNumber(sysTenant, sysUser.getId());
        Result<Integer> result = new Result<>();
        if(tenantId != 0){
            result.setMessage("申请加入组织成功");
            result.setSuccess(true);
            result.setResult(tenantId);
            return result;
        }else{
            result.setMessage("该门牌号不存在");
            result.setSuccess(false);
            return result;
        }
    }

    //update-begin---author:wangshuai ---date:20230107  for：[QQYUN-3725]申请加入租户，审核中状态增加接口------------
    /**
     * 分页获取租户用户数据(vue3用户租户页面)【低代码应用专用接口】
     *
     * @param pageNo
     * @param pageSize
     * @param userTenantStatus
     * @param type
     * @param req
     * @return
     */
    @GetMapping("/getUserTenantPageList")
    //@RequiresPermissions("system:tenant:tenantPageList")
    public Result<IPage<SysUserTenantVo>> getUserTenantPageList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                @RequestParam(name = "userTenantStatus") String userTenantStatus,
                                                                @RequestParam(name = "type", required = false) String type,
                                                                SysUser user,
                                                                HttpServletRequest req) {
        Page<SysUserTenantVo> page = new Page<SysUserTenantVo>(pageNo, pageSize);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String tenantId = oConvertUtils.getString(TenantContext.getTenant(), "0");
        IPage<SysUserTenantVo> list = relationService.getUserTenantPageList(page, Arrays.asList(userTenantStatus.split(SymbolConstant.COMMA)), user, Integer.valueOf(tenantId));
        return Result.ok(list);
    }

    /**
     * 通过用户id获取租户列表【低代码应用专用接口】
     *
     * @param userTenantStatus 关系表的状态
     * @return
     */
    @GetMapping("/getTenantListByUserId")
    //@RequiresPermissions("system:tenant:getTenantListByUserId")
    public Result<List<SysUserTenantVo>> getTenantListByUserId(@RequestParam(name = "userTenantStatus", required = false) String userTenantStatus) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> list = null;
        if (oConvertUtils.isNotEmpty(userTenantStatus)) {
            list = Arrays.asList(userTenantStatus.split(SymbolConstant.COMMA));
        }
        //租户状态，用户id,租户用户关系状态
        List<SysUserTenantVo> sysTenant = relationService.getTenantListByUserId(sysUser.getId(), list);
        return Result.ok(sysTenant);
    }

    /**
     * 更新用户租户关系状态【低代码应用专用接口】
     */
    @PutMapping("/updateUserTenantStatus")
    //@RequiresPermissions("system:tenant:updateUserTenantStatus")
    public Result<String> updateUserTenantStatus(@RequestBody SysUserTenant userTenant) {
        String tenantId = TenantContext.getTenant();
        if (oConvertUtils.isEmpty(tenantId)) {
            return Result.error("未找到当前租户信息");
        }
        relationService.updateUserTenantStatus(userTenant.getUserId(), tenantId, userTenant.getStatus());
        return Result.ok("更新用户租户状态成功");
    }

    /**
     * 注销租户【低代码应用专用接口】
     *
     * @param sysTenant
     * @return
     */
    @PutMapping("/cancelTenant")
    //@RequiresPermissions("system:tenant:cancelTenant")
    public Result<String> cancelTenant(@RequestBody SysTenant sysTenant,HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysTenant tenant = sysTenantService.getById(sysTenant.getId());
        if (null == tenant) {
            return Result.error("未找到当前租户信息");
        }
        if (!sysUser.getUsername().equals(tenant.getCreateBy())) {
            return Result.error("无权限，只能注销自己创建的租户！");
        }
        SysUser userById = sysUserService.getById(sysUser.getId());
        String loginPassword = request.getParameter("loginPassword");
        String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(),loginPassword, userById.getSalt());
        if (!passwordEncode.equals(userById.getPassword())) {
            return Result.error("密码不正确");
        }
        sysTenantService.removeById(sysTenant.getId());
        return Result.ok("注销成功");
    }
    //update-end---author:wangshuai ---date:20230107  for：[QQYUN-3725]申请加入租户，审核中状态增加接口------------

    /**
     * 获取租户用户不同状态下的数量【低代码应用专用接口】
     * @return
     */
    @GetMapping("/getTenantStatusCount")
    public Result<Long> getTenantStatusCount(@RequestParam(value = "status",defaultValue = "1") String status, HttpServletRequest req){
        String tenantId = TokenUtils.getTenantIdByRequest(req);
        if (null == tenantId) {
            return Result.error("未找到当前租户信息");
        }
        LambdaQueryWrapper<SysUserTenant> query = new LambdaQueryWrapper<>();
        query.eq(SysUserTenant::getTenantId,tenantId);
        query.eq(SysUserTenant::getStatus,status);
        long count = relationService.count(query);
        return Result.ok(count);
    }

    /**
     * 用户取消租户申请【低代码应用专用接口】
     * @param tenantId
     * @return
     */
    @PutMapping("/cancelApplyTenant")
    public Result<String> cancelApplyTenant(@RequestParam("tenantId") String tenantId){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        sysTenantService.leaveTenant(sysUser.getId(),tenantId);
        return Result.ok("取消申请成功");
    }

    //===========【低代码应用，前端专用接口 —— 加入限制只能维护和查看自己拥有的租户】==========================================================

    /**
     * 彻底删除租户
     * @param ids
     * @return
     */
    @DeleteMapping("/deleteLogicDeleted")
    @RequiresPermissions("system:tenant:deleteTenantLogic")
    public Result<String> deleteTenantLogic(@RequestParam("ids") String ids){
        sysTenantService.deleteTenantLogic(ids);
        return Result.ok("彻底删除成功");
    }

    /**
     * 还原删除的租户
     * @param ids
     * @return
     */
    @PutMapping("/revertTenantLogic")
    @RequiresPermissions("system:tenant:revertTenantLogic")
    public Result<String> revertTenantLogic(@RequestParam("ids") String ids){
        sysTenantService.revertTenantLogic(ids);
        return Result.ok("还原成功");
    }

    /**
     * 退出租户【低代码应用专用接口】
     * @param sysTenant
     * @param request
     * @return
     */
    @DeleteMapping("/exitUserTenant")
    public Result<String> exitUserTenant(@RequestBody SysTenant sysTenant,HttpServletRequest request){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //验证用户是否已存在
        Integer count = relationService.userTenantIzExist(sysUser.getId(),sysTenant.getId());
        if (count == 0) {
            return Result.error("此租户下没有当前用户");
        }
        //验证密码
        String loginPassword = request.getParameter("loginPassword");
        SysUser userById = sysUserService.getById(sysUser.getId());
        String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(),loginPassword, userById.getSalt());
        if (!passwordEncode.equals(userById.getPassword())) {
            return Result.error("密码不正确");
        }
        //退出登录
        sysTenantService.exitUserTenant(sysUser.getId(),sysUser.getUsername(),String.valueOf(sysTenant.getId()));
        return Result.ok("退出租户成功");
    }

    /**
     * 变更租户拥有者【低代码应用专用接口】
     * @param userId
     * @return
     */
    @PostMapping("/changeOwenUserTenant")
    public Result<String> changeOwenUserTenant(@RequestParam("userId") String userId,
                                               @RequestParam("tenantId") String tenantId){
        sysTenantService.changeOwenUserTenant(userId,tenantId);
        return Result.ok("退出租户成功");
    }

    /**
     * 邀请用户到租户,通过手机号匹配 【低代码应用专用接口】
     * @param phone
     * @param departId
     * @return
     */
    @PostMapping("/invitationUser")
    public Result<String> invitationUser(@RequestParam(name="phone") String phone,
                                         @RequestParam(name="departId",defaultValue = "") String departId){
        return sysTenantService.invitationUser(phone,departId);
    }


    /**
     * 获取 租户产品包-3个默认admin的人员数量
     * @param tenantId
     * @return
     */
    @GetMapping("/loadAdminPackCount")
    public Result<List<TenantPackUserCount>> loadAdminPackCount(@RequestParam("tenantId") Integer tenantId){
        List<TenantPackUserCount> list = sysTenantService.queryTenantPackUserCount(tenantId);
        return Result.ok(list);
    }

    /**
     * 查询租户产品包信息
     * @param packModel
     * @return
     */
    @GetMapping("/getTenantPackInfo")
    public Result<TenantPackModel> getTenantPackInfo(TenantPackModel packModel){
        TenantPackModel tenantPackModel = sysTenantService.queryTenantPack(packModel);
        return Result.ok(tenantPackModel);
    }


    /**
     * 添加用户和产品包的关系数据
     * @param sysTenantPackUser
     * @return
     */
    @PostMapping("/addTenantPackUser")
    public Result<?> addTenantPackUser(@RequestBody SysTenantPackUser sysTenantPackUser){
        sysTenantService.addBatchTenantPackUser(sysTenantPackUser);
        return Result.ok("操作成功！");
    }

    /**
     * 从产品包移除用户
     * @param sysTenantPackUser
     * @return
     */
    @PutMapping("/deleteTenantPackUser")
    public Result<?> deleteTenantPackUser(@RequestBody SysTenantPackUser sysTenantPackUser){
        sysTenantService.deleteTenantPackUser(sysTenantPackUser);
        return Result.ok("操作成功！");
    }


    /**
     * 修改申请状态
     * @param sysTenant
     * @return
     */
    @PutMapping("/updateApplyStatus")
    public Result<?> updateApplyStatus(@RequestBody SysTenant sysTenant){
        SysTenant entity = this.sysTenantService.getById(sysTenant.getId());
        if(entity==null){
            return Result.error("租户不存在!");
        }
        entity.setApplyStatus(sysTenant.getApplyStatus());
        sysTenantService.updateById(entity);
        return Result.ok("");
    }


    /**
     * 获取产品包人员申请列表
     * @param tenantId
     * @return
     */
    @GetMapping("/getTenantPackApplyUsers")
    public Result<?> getTenantPackApplyUsers(@RequestParam("tenantId") Integer tenantId){
        List<TenantPackUser> list = sysTenantService.getTenantPackApplyUsers(tenantId);
        return Result.ok(list);
    }

    /**
     * 个人 申请成为管理员
     * @param sysTenantPackUser
     * @return
     */
    @PostMapping("/doApplyTenantPackUser")
    public Result<?> doApplyTenantPackUser(@RequestBody SysTenantPackUser sysTenantPackUser){
        sysTenantService.doApplyTenantPackUser(sysTenantPackUser);
        return Result.ok("申请成功！");
    }

    /**
     * 申请通过 成为管理员
     * @param sysTenantPackUser
     * @return
     */
    @PutMapping("/passApply")
    public Result<?> passApply(@RequestBody SysTenantPackUser sysTenantPackUser){
        sysTenantService.passApply(sysTenantPackUser);
        return Result.ok("操作成功！");
    }

    /**
     *  拒绝申请 成为管理员
     * @param sysTenantPackUser
     * @return
     */
    @PutMapping("/deleteApply")
    public Result<?> deleteApply(@RequestBody SysTenantPackUser sysTenantPackUser){
        sysTenantService.deleteApply(sysTenantPackUser);
        return Result.ok("");
    }

    /**
     * 查看是否已经申请过了超级管理员
     * @return
     */
    @GetMapping("/getApplySuperAdminCount")
    public Result<Long> getApplySuperAdminCount(){
        Long count = sysTenantService.getApplySuperAdminCount();
        return Result.ok(count);
    }

    /**
     * 进入应用组织页面 查询租户信息及当前用户是否有 管理员的权限--
     * @param id
     * @return
     */
    @RequestMapping(value = "/queryTenantAuthInfo", method = RequestMethod.GET)
    public Result<TenantDepartAuthInfo> queryTenantAuthInfo(@RequestParam(name="id",required=true) String id) {
        TenantDepartAuthInfo info = sysTenantService.getTenantDepartAuthInfo(Integer.parseInt(id));
        return Result.ok(info);
    }

    /**
     * 获取产品包下的用户列表(分页)
     * @param tenantId
     * @param packId
     * @param status
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/queryTenantPackUserList")
    public Result<IPage<TenantPackUser>> queryTenantPackUserList(@RequestParam("tenantId") String tenantId,
                                                                 @RequestParam("packId") String packId,
                                                                 @RequestParam("status") Integer status,
                                                                 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                                 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize){
        Page<TenantPackUser> page = new Page<>(pageNo,pageSize);
        IPage<TenantPackUser> pageList = sysTenantService.queryTenantPackUserList(tenantId,packId,status,page);
        return Result.ok(pageList);
    }

    /**
     * 获取当前租户下的部门和成员数量
     */
    @GetMapping("/getTenantCount")
    public Result<Map<String,Long>> getTenantCount(HttpServletRequest request){
        Map<String,Long> map = new HashMap<>();
        //update-begin---author:wangshuai---date:2023-11-24---for:【QQYUN-7177】用户数量显示不正确---
        if(oConvertUtils.isEmpty(TokenUtils.getTenantIdByRequest(request))){
            return Result.error("当前租户为空，禁止访问！");
        }
        Integer tenantId = oConvertUtils.getInt(TokenUtils.getTenantIdByRequest(request));
        Long userCount = relationService.getUserCount(tenantId,CommonConstant.USER_TENANT_NORMAL);
        //update-end---author:wangshuai---date:2023-11-24---for:【QQYUN-7177】用户数量显示不正确---
        map.put("userCount",userCount);
        LambdaQueryWrapper<SysDepart> departQuery = new LambdaQueryWrapper<>();
        departQuery.eq(SysDepart::getDelFlag,String.valueOf(CommonConstant.DEL_FLAG_0));
        departQuery.eq(SysDepart::getTenantId,tenantId);
        //部门状态暂时没用，先注释掉
        //departQuery.eq(SysDepart::getStatus,CommonConstant.STATUS_1);
        long departCount = sysDepartService.count(departQuery);
        map.put("departCount",departCount);
        return Result.ok(map);
    }

    /**
     * 通过用户id获取租户列表（分页）
     *
     * @param sysUserTenantVo
     * @return
     */
    @GetMapping("/getTenantPageListByUserId")
    public Result<IPage<SysTenant>> getTenantPageListByUserId(SysUserTenantVo sysUserTenantVo,
                                                              @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                              @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> list = null;
        String userTenantStatus = sysUserTenantVo.getUserTenantStatus();
        if (oConvertUtils.isNotEmpty(userTenantStatus)) {
            list = Arrays.asList(userTenantStatus.split(SymbolConstant.COMMA));
        }
        Page<SysTenant> page = new Page<>(pageNo,pageSize);
        IPage<SysTenant> pageList = relationService.getTenantPageListByUserId(page,sysUser.getId(),list,sysUserTenantVo);
        return Result.ok(pageList);
    }

    /**
     * 同意或拒绝加入租户
     */
    @PutMapping("/agreeOrRefuseJoinTenant")
    public Result<String> agreeOrRefuseJoinTenant(@RequestParam("tenantId") Integer tenantId,
                                                  @RequestParam("status") String status){
        //是否开启系统管理模块的多租户数据隔离【SAAS多租户模式】
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userId = sysUser.getId();
        SysTenant tenant = sysTenantService.getById(tenantId);
        if(null == tenant){
            return Result.error("不存在该组织");
        }
        SysUserTenant sysUserTenant = relationService.getUserTenantByTenantId(userId, tenantId);
        if (null == sysUserTenant) {
            return Result.error("该用户不存在该组织中，无权修改");
        }
        String content = "";
        SysUser user = new SysUser();
        user.setUsername(sysUserTenant.getCreateBy());
        String realname = oConvertUtils.getString(sysUser.getRealname(),sysUser.getUsername());
        //成功加入
        if(CommonConstant.USER_TENANT_NORMAL.equals(status)){
            //修改租户状态
            relationService.agreeJoinTenant(userId,tenantId);
            content = content + realname + "已同意您发送的加入 " + tenant.getName() + " 的邀请";
            sysTenantService.sendMsgForAgreeAndRefuseJoin(user, content);
            return Result.OK("您已同意该组织的邀请");
        }else if(CommonConstant.USER_TENANT_REFUSE.equals(status)){
            //直接删除关系表即可
            relationService.refuseJoinTenant(userId,tenantId);
            content = content + realname + "拒绝了您发送的加入 " + tenant.getName() + " 的邀请";
            sysTenantService.sendMsgForAgreeAndRefuseJoin(user, content);
            return Result.OK("您已成功拒绝该组织的邀请");
        }
        return Result.error("类型不匹配，禁止修改数据");
    }

    /**
     * 目前只给敲敲云租户下删除用户使用
     *
     * 根据密码删除用户
     */
    @DeleteMapping("/deleteUserByPassword")
    public Result<String> deleteUserByPassword(@RequestBody SysUser sysUser,HttpServletRequest request){
        Integer tenantId = oConvertUtils.getInteger(TokenUtils.getTenantIdByRequest(request), null);
        sysTenantService.deleteUserByPassword(sysUser, tenantId);
        return Result.ok("删除用户成功");
    }

    /**
     *  查询当前用户的所有有效租户【知识库专用接口】
     * @return
     */
    @RequestMapping(value = "/getCurrentUserTenantForFile", method = RequestMethod.GET)
    public Result<Map<String,Object>> getCurrentUserTenantForFile() {
        Result<Map<String,Object>> result = new Result<Map<String,Object>>();
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            List<SysTenant> tenantList = sysTenantService.getTenantListByUserId(sysUser.getId());
            Map<String,Object> map = new HashMap<>(5);
            //在开启saas租户隔离的时候并且租户数据不为空，则返回租户信息
            if (MybatisPlusSaasConfig.OPEN_SYSTEM_TENANT_CONTROL && CollectionUtil.isNotEmpty(tenantList)) {
                map.put("list", tenantList);
            }
            result.setSuccess(true);
            result.setResult(map);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            result.error500("查询失败！");
        }
        return result;
    }

    /**
     * 批量处理租户用户数据 - 优化版本
     * 使用批量操作提高性能，减少数据库交互次数
     *
     * @param list 导入的租户用户数据列表
     * @return 成功处理的数据条数
     */
    private int processTenantUserDataBatch(List<SysAddTenantUserDto> list) {
        if (CollectionUtil.isEmpty(list)) {
            return 0;
        }

        // 1. 数据预处理 - 收集所有需要的ID
        Set<Integer> tenantIds = list.stream()
                .map(SysAddTenantUserDto::getTenantCode)
                .collect(Collectors.toSet());

        Set<String> userNames = list.stream()
                .filter(item -> oConvertUtils.isNotEmpty(item.getUserName()))
                .flatMap(item -> Arrays.stream(item.getUserName().split(",")))
                .map(String::trim)
                .filter(oConvertUtils::isNotEmpty)
                .collect(Collectors.toSet());

        Set<String> roleCodes = list.stream()
                .filter(item -> oConvertUtils.isNotEmpty(item.getRoleCode()))
                .map(SysAddTenantUserDto::getRoleCode)
                .collect(Collectors.toSet());

        // 2. 批量查询现有数据
        Map<Integer, SysTenant> existingTenants = sysTenantService.listByIds(tenantIds)
                .stream()
                .collect(Collectors.toMap(SysTenant::getId, tenant -> tenant));

        Map<String, SysUser> existingUsers = sysUserService.lambdaQuery()
                .in(SysUser::getUsername, userNames)
                .list()
                .stream()
                .collect(Collectors.toMap(SysUser::getUsername, user -> user));

        Map<String, SysRole> existingRoles = sysRoleService.lambdaQuery()
                .in(SysRole::getRoleCode, roleCodes)
                .list()
                .stream()
                .collect(Collectors.toMap(SysRole::getRoleCode, role -> role));

        // 3. 准备批量操作的数据集合
        List<SysTenant> tenantsToSave = new ArrayList<>();
        List<SysTenant> tenantsToUpdate = new ArrayList<>();
        List<SysUser> usersToSave = new ArrayList<>();
        List<SysUserRole> userRolesToSave = new ArrayList<>();
        List<SysUserTenant> userTenantsToSave = new ArrayList<>();
        List<SysDictItem> dictItemsToSave = new ArrayList<>();

        // 获取当前登录用户
        LoginUser currentUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String currentUsername = currentUser != null ? currentUser.getUsername() : null;
        Date currentTime = new Date();

        // 获取租户字典
        SysDict tenantDict = sysDictService.lambdaQuery()
                .eq(SysDict::getDictCode, "tenant_company")
                .one();

        int successCount = 0;

        // 4. 处理每条数据
        for (SysAddTenantUserDto item : list) {
            try {
                // 处理租户
                SysTenant tenant = existingTenants.get(item.getTenantCode());
                if (tenant == null) {
                    // 创建新租户
                    tenant = createNewTenant(item, currentUsername, currentTime);
                    tenantsToSave.add(tenant);
                    existingTenants.put(tenant.getId(), tenant);

                    // 准备字典项
                    if (tenantDict != null) {
                        SysDictItem dictItem = new SysDictItem();
                        dictItem.setStatus(1)
                                .setDictId(tenantDict.getId())
                                .setItemText(item.getTenantName())
                                .setItemValue(String.valueOf(tenant.getId()));
                        dictItemsToSave.add(dictItem);
                    }
                } else {
                    // 更新现有租户
                    updateExistingTenant(tenant, item, currentUsername, currentTime);
                    tenantsToUpdate.add(tenant);
                }

                // 处理用户
                if (oConvertUtils.isNotEmpty(item.getUserName())) {
                    String[] userNameArray = item.getUserName().split(",");
                    for (String userName : userNameArray) {
                        userName = userName.trim();
                        if (oConvertUtils.isEmpty(userName)) {
                            continue;
                        }

                        SysUser user = existingUsers.get(userName);
                        if (user == null) {
                            // 创建新用户
                            user = createNewUser(userName, currentTime,  tenant.getId());
                            sysUserService.save(user);
                            existingUsers.put(userName, user);

                            // 准备用户角色关系
                            if (oConvertUtils.isNotEmpty(item.getRoleCode())) {
                                SysRole role = existingRoles.get(item.getRoleCode());
                                if (role != null) {
                                    SysUserRole userRole = new SysUserRole();
                                    userRole.setRoleId(role.getId());
                                    userRole.setUserId(user.getId());
                                    userRolesToSave.add(userRole);
                                }
                            }
                        }

                        // 准备用户租户关系
                        SysUserTenant userTenant = new SysUserTenant();
                        userTenant.setUserId(user.getId());
                        userTenant.setTenantId(tenant.getId());
                        userTenant.setStatus(CommonConstant.USER_TENANT_NORMAL);
                        userTenantsToSave.add(userTenant);
                        successCount++;
                    }
                }
            } catch (Exception e) {
                log.error("处理租户用户数据失败: {}", item.getTenantName(), e);
            }
        }

        // 5. 执行批量操作
        executeBatchOperations(tenantsToSave, tenantsToUpdate, usersToSave,
                              userRolesToSave, userTenantsToSave, dictItemsToSave);

        return successCount;
    }

    /**
     * 创建新租户
     */
    private SysTenant createNewTenant(SysAddTenantUserDto item, String currentUsername, Date currentTime) {
        SysTenant tenant = new SysTenant();
        tenant.setId(item.getTenantCode());
        tenant.setName(item.getTenantName());

        // 设置基本信息
        tenant.setStatus(1);
        tenant.setHouseNumber(RandomUtil.randomStringUpper(6));
        tenant.setDelFlag(CommonConstant.DEL_FLAG_0);
        tenant.setCreateTime(currentTime);
        tenant.setCreateBy(currentUsername);

        // 设置从Excel导入的字段
        tenant.setLegalPerson(item.getLegalPerson());
        tenant.setRegisteredCapital(item.getRegisteredCapital());
        tenant.setRegTime(item.getEstablishmentDate());
        tenant.setCreditCode(item.getCreditCode());
        tenant.setIntro(item.getCompanyProfile());
        tenant.setCompanyAddress(item.getCompanyAddress());
        tenant.setWebsite(item.getWebsite());
        tenant.setIpAddress(item.getIpAddress());
        tenant.setSortOrder(item.getSortOrder());
        tenant.setEmail(item.getContactEmail());

        // 处理业务类型转换
        tenant.setBusType(convertBusType(item.getBusType()));

        // 设置是否显示
        if (item.getIsVisible() != null) {
            tenant.setIsShow("是".equals(item.getIsVisible()) ? 1 : 0);
        }

        return tenant;
    }

    /**
     * 更新现有租户
     */
    private void updateExistingTenant(SysTenant tenant, SysAddTenantUserDto item, String currentUsername, Date currentTime) {
        // 更新租户信息
        if (item.getLegalPerson() != null) tenant.setLegalPerson(item.getLegalPerson());
        if (item.getRegisteredCapital() != null) tenant.setRegisteredCapital(item.getRegisteredCapital());
        if (item.getEstablishmentDate() != null) tenant.setRegTime(item.getEstablishmentDate());
        if (item.getCreditCode() != null) tenant.setCreditCode(item.getCreditCode());
        if (item.getCompanyProfile() != null) tenant.setIntro(item.getCompanyProfile());
        if (item.getCompanyAddress() != null) tenant.setCompanyAddress(item.getCompanyAddress());
        if (item.getWebsite() != null) tenant.setWebsite(item.getWebsite());
        if (item.getContactEmail() != null) tenant.setEmail(item.getContactEmail());
        if (item.getSortOrder() != null) tenant.setSortOrder(item.getSortOrder());
        if (item.getIpAddress() != null) tenant.setIpAddress(item.getIpAddress());

        // 处理业务类型转换
        if (item.getBusType() != null && !item.getBusType().isEmpty()) {
            tenant.setBusType(convertBusType(item.getBusType()));
        }

        // 设置是否显示
        if (item.getIsVisible() != null) {
            tenant.setIsShow("是".equals(item.getIsVisible()) ? 1 : 0);
        }

        // 更新时间和操作人
        tenant.setUpdateTime(currentTime);
        tenant.setUpdateBy(currentUsername);
    }

    /**
     * 创建新用户
     */
    private SysUser createNewUser(String userName, Date currentTime, Integer bindTenant) {
        SysUser user = new SysUser();
        user.setUsername(userName);
        user.setRealname(userName);
        // 设置随机工号
        user.setWorkNo(RandomUtil.randomNumbers(8));
        // 设置随机手机号
        //user.setPhone("13" + RandomUtil.randomNumbers(9));
        // 设置随机邮箱
        user.setEmail(userName + "@example.com");
        // 设置密码为8888
        String salt = oConvertUtils.randomGen(8);
        user.setSalt(salt);
        String passwordEncode = PasswordUtil.encrypt(user.getUsername(), "8888", salt);
        user.setPassword(passwordEncode);
        user.setBindTenant(String.valueOf(bindTenant));
        user.setStatus(1);
        user.setDelFlag(CommonConstant.DEL_FLAG_0);
        user.setCreateTime(currentTime);

        return user;
    }

    /**
     * 业务类型转换：将"车险，增值服务，财险"转换为"0,2,1"
     */
    private String convertBusType(String busType) {
        if (busType == null || busType.isEmpty()) {
            return null;
        }

        StringBuilder busTypeCode = new StringBuilder();
        String[] busTypes = busType.split(",|，"); // 同时处理中英文逗号

        for (int i = 0; i < busTypes.length; i++) {
            String type = busTypes[i].trim();
            if ("车险".equals(type)) {
                busTypeCode.append("0");
            } else if ("财险".equals(type)) {
                busTypeCode.append("1");
            } else if ("增值服务".equals(type)) {
                busTypeCode.append("2");
            }

            // 如果不是最后一个元素，添加逗号
            if (i < busTypes.length - 1 && busTypeCode.length() > 0) {
                busTypeCode.append(",");
            }
        }

        return busTypeCode.toString();
    }

    /**
     * 执行批量操作
     */
    private void executeBatchOperations(List<SysTenant> tenantsToSave,
                                       List<SysTenant> tenantsToUpdate,
                                       List<SysUser> usersToSave,
                                       List<SysUserRole> userRolesToSave,
                                       List<SysUserTenant> userTenantsToSave,
                                       List<SysDictItem> dictItemsToSave) {
        try {
            // 1. 批量保存新租户
            if (!tenantsToSave.isEmpty()) {
                sysTenantService.saveBatch(tenantsToSave);
                log.info("批量保存租户成功，数量: {}", tenantsToSave.size());

                // 为新租户添加默认产品包
                for (SysTenant tenant : tenantsToSave) {
                    try {
                        sysTenantPackService.addTenantDefaultPack(tenant.getId());
                    } catch (Exception e) {
                        log.error("为租户 {} 添加默认产品包失败", tenant.getId(), e);
                    }
                }
            }

            // 2. 批量更新现有租户
            if (!tenantsToUpdate.isEmpty()) {
                sysTenantService.saveOrUpdateBatch(tenantsToUpdate);
                log.info("批量更新租户成功，数量: {}", tenantsToUpdate.size());
            }

            // 3. 批量保存新用户
            if (!usersToSave.isEmpty()) {
                sysUserService.saveOrUpdateBatch(usersToSave);
                log.info("批量保存用户成功，数量: {}", usersToSave.size());
            }

            // 4. 批量保存用户角色关系
            if (!userRolesToSave.isEmpty()) {
                sysUserRoleService.saveOrUpdateBatch(userRolesToSave);
                log.info("批量保存用户角色关系成功，数量: {}", userRolesToSave.size());
            }

            // 5. 批量保存用户租户关系（去重处理）
            if (!userTenantsToSave.isEmpty()) {
                // 查询已存在的用户租户关系，避免重复插入
                Set<String> existingRelations = new HashSet<>();
                for (SysUserTenant userTenant : userTenantsToSave) {
                    LambdaQueryWrapper<SysUserTenant> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(SysUserTenant::getUserId, userTenant.getUserId())
                           .eq(SysUserTenant::getTenantId, userTenant.getTenantId());
                    long count = relationService.count(wrapper);
                    if (count == 0) {
                        existingRelations.add(userTenant.getUserId() + "_" + userTenant.getTenantId());
                    }
                }

                // 过滤掉已存在的关系
                List<SysUserTenant> filteredUserTenants = userTenantsToSave.stream()
                        .filter(ut -> existingRelations.contains(ut.getUserId() + "_" + ut.getTenantId()))
                        .collect(Collectors.toList());

                if (!filteredUserTenants.isEmpty()) {
                    relationService.saveOrUpdateBatch(filteredUserTenants);
                    log.info("批量保存用户租户关系成功，数量: {}", filteredUserTenants.size());
                }
            }

            // 6. 批量保存字典项
//            if (!dictItemsToSave.isEmpty()) {
//                sysDictItemService.saveBatch(dictItemsToSave);
//                log.info("批量保存字典项成功，数量: {}", dictItemsToSave.size());
//            }

            // 7. 批量处理点击数（如果需要的话）
            // 这里可以根据需要添加点击数的批量处理逻辑

        } catch (Exception e) {
            log.error("执行批量操作失败", e);
            throw new RuntimeException("批量操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取租户业务城市
     * @param tenantId 租户ID
     * @return 城市编码列表
     */
    @ApiOperation(value = "获取租户业务城市")
    @GetMapping("/businessCities")
    public Result<List<String>> getTenantBusinessCities(@RequestParam("tenantId") String tenantId) {
        try {
            if (oConvertUtils.isEmpty(tenantId)) {
                return Result.error("租户ID不能为空");
            }

            List<String> cityCodes = tenantCityRelationService.getCityCodesByTenantId(Integer.valueOf(tenantId));
            return Result.OK(cityCodes);
        } catch (Exception e) {
            log.error("获取租户业务城市失败", e);
            return Result.error("获取租户业务城市失败: " + e.getMessage());
        }
    }

    /**
     * 保存租户业务城市
     * @param request 请求参数
     * @return 保存结果
     */
    @ApiOperation(value = "保存租户业务城市")
    @PostMapping("/businessCities")
    public Result<String> saveTenantBusinessCities(@RequestBody Map<String, Object> request) {
        try {
            Object tenantIdObj = request.get("tenantId");
            Object cityCodesObj = request.get("cityCodes");

            if (tenantIdObj == null) {
                return Result.error("租户ID不能为空");
            }

            Integer tenantId = Integer.valueOf(tenantIdObj.toString());
            List<String> cityCodes = new ArrayList<>();

            if (cityCodesObj instanceof List) {
                List<?> cityCodesList = (List<?>) cityCodesObj;
                for (Object code : cityCodesList) {
                    if (code != null && oConvertUtils.isNotEmpty(code.toString())) {
                        cityCodes.add(code.toString().trim());
                    }
                }
            }

            log.info("保存租户业务城市，tenantId: {}, cityCodes: {}", tenantId, cityCodes);
            tenantCityRelationService.saveTenantCityRelations(tenantId, cityCodes);

            return Result.OK("保存成功");
        } catch (Exception e) {
            log.error("保存租户业务城市失败", e);
            return Result.error("保存租户业务城市失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户业务类型
     * @param tenantId 租户ID
     * @return 业务类型列表
     */
    @ApiOperation(value = "获取租户业务类型")
    @GetMapping("/businessTypes")
    public Result<List<Integer>> getTenantBusinessTypes(@RequestParam("tenantId") String tenantId) {
        try {
            if (oConvertUtils.isEmpty(tenantId)) {
                return Result.error("租户ID不能为空");
            }

            List<Integer> businessTypes = tenantBusinessTypeRelationService.getBusinessTypesByTenantId(Integer.valueOf(tenantId));
            return Result.OK(businessTypes);
        } catch (Exception e) {
            log.error("获取租户业务类型失败", e);
            return Result.error("获取租户业务类型失败: " + e.getMessage());
        }
    }

    /**
     * 保存租户业务类型
     * @param request 请求参数
     * @return 保存结果
     */
    @ApiOperation(value = "保存租户业务类型")
    @PostMapping("/businessTypes")
    public Result<String> saveTenantBusinessTypes(@RequestBody Map<String, Object> request) {
        try {
            Object tenantIdObj = request.get("tenantId");
            Object businessTypesObj = request.get("businessTypes");

            if (tenantIdObj == null) {
                return Result.error("租户ID不能为空");
            }

            Integer tenantId = Integer.valueOf(tenantIdObj.toString());
            List<Integer> businessTypes = new ArrayList<>();

            if (businessTypesObj instanceof List) {
                List<?> businessTypesList = (List<?>) businessTypesObj;
                for (Object type : businessTypesList) {
                    if (type != null) {
                        try {
                            Integer businessType = Integer.valueOf(type.toString());
                            // 验证业务类型值是否有效 (0-车险,1-财险,2-增值服务)
                            if (businessType >= 0 && businessType <= 2) {
                                businessTypes.add(businessType);
                            }
                        } catch (NumberFormatException e) {
                            log.warn("无效的业务类型值: {}", type);
                        }
                    }
                }
            }

            log.info("保存租户业务类型，tenantId: {}, businessTypes: {}", tenantId, businessTypes);
            tenantBusinessTypeRelationService.saveTenantBusinessTypeRelations(tenantId, businessTypes);

            return Result.OK("保存成功");
        } catch (Exception e) {
            log.error("保存租户业务类型失败", e);
            return Result.error("保存租户业务类型失败: " + e.getMessage());
        }
    }
}
