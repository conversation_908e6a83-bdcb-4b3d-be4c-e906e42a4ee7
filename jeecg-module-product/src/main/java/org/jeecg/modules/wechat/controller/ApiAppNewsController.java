package org.jeecg.modules.wechat.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.wechat.dto.AppNewsListDto;
import org.jeecg.modules.wechat.entity.AppNews;
import org.jeecg.modules.wechat.service.IAppNewsService;
import org.jeecg.modules.wechat.service.IWeChatService;
import org.jeecg.modules.wh.service.IWhRecordStatisticsService;
import org.jeecg.modules.wh.vo.WhRecordStatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Api(tags="一期移动端-app资讯")
@RestController
@RequestMapping("api/wechat/appNews")
@Slf4j
public class ApiAppNewsController  extends JeecgController<AppNews, IAppNewsService> {
    @Autowired
    private IAppNewsService appNewsService;

    @Autowired
    private IWhClickService whClickService;
    @Resource
    private IWeChatService weChatService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private IWhRecordStatisticsService whRecordStatisticsService;

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    //@AutoLog(value = "app资讯-分页列表查询")
    @ApiOperation(value="app资讯-分页列表查询", notes="app资讯-分页列表查询")
    @PostMapping(value = "/list/{pageNo}/{pageSize}")
    public Result<IPage<AppNews>> queryPageList( @PathVariable(name="pageNo") Integer pageNo, @PathVariable(name="pageSize") Integer pageSize,
                                                 @RequestBody AppNewsListDto dto) {
        Page<AppNews> page = new Page<AppNews>(pageNo, pageSize);
        IPage<AppNews> pageList = appNewsService.queryPageList(page, dto);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询
     *
     * @return
     */
    //@AutoLog(value = "app资讯-分页列表查询")
    @ApiOperation(value="app资讯-分页列表查询", notes="app资讯-分页列表查询")
    @PostMapping(value = "/list")
    public Result<List<AppNews>> list(
            @RequestBody AppNewsListDto dto) {
        //获取配置,若为上架页面
        String shelfConfig = weChatService.getShelfConfig();
        LambdaQueryWrapper<AppNews> queryWrapper = new LambdaQueryWrapper<>();
        if (shelfConfig.equals("1")){
            if (ObjectUtil.isNotEmpty(dto.getType())) {
                if (dto.getType().contains("4")) {
                    queryWrapper.eq(AppNews::getType, 4);
                }else {
                    queryWrapper.eq(AppNews::getType, 100);
                }

            }else {
                queryWrapper.eq(AppNews::getType, 100);
            }
        }else {
            if (ObjectUtil.isNotEmpty(dto.getType())) {
                queryWrapper.in(AppNews::getType, dto.getType());
            }
            if (StrUtil.isNotEmpty(dto.getName())) {
                queryWrapper.like(AppNews::getName, dto.getName());
            }
        }

        List<AppNews> list = appNewsService.list();
        list.forEach(item->{
                    if (StrUtil.isEmpty(item.getClicksId())){return;}
                    WhClick whClick = whClickService.getById(item.getClicksId());
                    if (whClick!=null){
                        item.setClicksNum(whClick.getWhNum());
                    }
                }
        );
        return Result.OK(list);
    }




    @ApiOperation(value = "app资讯-通过id查询", notes = "app资讯-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<AppNews> queryById(@RequestParam(name = "id", required = true) String id, HttpServletRequest request) {
        // 查询文章
        AppNews appNews = appNewsService.getById(id);
        if (appNews == null) {
            return Result.error("未找到对应数据");
        }

        // 获取用户 IP
        String userIp = getClientIp(request);

        // Redis key 结构：文章ID作为key，value是IP列表
        String redisKey = "article:clicks:" + id;

        // 判断 IP 是否已经点击过
        Boolean hasClicked = redisTemplate.opsForSet().isMember(redisKey, userIp);
        if (!hasClicked) {
            // 如果没有点击过，记录点击量并将 IP 添加到 Redis
            if (StrUtil.isNotEmpty(appNews.getClicksId())) {
                whClickService.addNum(appNews.getClicksId());
            }
            redisTemplate.opsForSet().add(redisKey, userIp);
            // 设置过期时间，例如：24小时
            redisTemplate.expire(redisKey, 24, TimeUnit.HOURS);

        }
            WhClick byId = whClickService.getById(appNews.getClicksId());
            appNews.setClicksNum(byId.getWhNum());

        // 查询统计信息
        try {
            WhRecordStatisticsVO statisticsVO = whRecordStatisticsService.getStatisticsByPid(id);
            if (statisticsVO != null) {
                appNews.setBrowseCount(statisticsVO.getBrowseCount());
                appNews.setFavoriteCount(statisticsVO.getFavoriteCount());
                appNews.setLikeCount(statisticsVO.getLikeCount());
                appNews.setShareCount(statisticsVO.getShareCount());
            } else {
                // 如果没有统计数据，设置为0
                appNews.setBrowseCount(0);
                appNews.setFavoriteCount(0);
                appNews.setLikeCount(0);
                appNews.setShareCount(0);
            }
        } catch (Exception e) {
            log.error("查询统计信息失败，资讯ID：{}", id, e);
            // 出现异常时设置为0
            appNews.setBrowseCount(0);
            appNews.setFavoriteCount(0);
            appNews.setLikeCount(0);
            appNews.setShareCount(0);
        }

        return Result.OK(appNews);
    }

    // 获取客户端 IP
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isNotEmpty(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip.split(",")[0];
        }
        ip = request.getHeader("Proxy-Client-IP");
        if (StrUtil.isNotEmpty(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (StrUtil.isNotEmpty(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        return request.getRemoteAddr();
    }

}
