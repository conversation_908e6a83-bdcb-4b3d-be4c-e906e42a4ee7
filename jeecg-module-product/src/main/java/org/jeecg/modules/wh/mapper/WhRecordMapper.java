package org.jeecg.modules.wh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.wh.entity.WhRecord;
import org.jeecg.modules.wh.dto.WhRecordQueryDTO;
import org.jeecg.modules.wh.vo.WhRecordVO;

import java.util.List;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
public interface WhRecordMapper extends BaseMapper<WhRecord> {

    /**
     * 根据用户ID和记录类型查询记录
     * @param userId 用户ID
     * @param recordType 记录类型
     * @return 记录列表
     */
    List<WhRecord> findByUserIdAndRecordType(@Param("userId") String userId, @Param("recordType") Integer recordType);

    /**
     * 根据用户ID查询所有记录
     * @param userId 用户ID
     * @return 记录列表
     */
    List<WhRecord> findByUserId(@Param("userId") String userId);

    /**
     * 根据租户ID查询记录
     * @param tenantId 租户ID
     * @return 记录列表
     */
    List<WhRecord> findByTenantId(@Param("tenantId") Integer tenantId);

    /**
     * 根据内容ID和内容类型查询记录
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 记录列表
     */
    List<WhRecord> findByContentIdAndContentType(@Param("contentId") String contentId, @Param("contentType") Integer contentType);

    /**
     * 删除用户指定类型的记录
     * @param userId 用户ID
     * @param recordType 记录类型
     * @return 删除数量
     */
    int deleteByUserIdAndRecordType(@Param("userId") String userId, @Param("recordType") Integer recordType);

    /**
     * 删除用户指定内容的记录
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @return 删除数量
     */
    int deleteByUserIdAndContent(@Param("userId") String userId, @Param("contentId") String contentId, 
                                @Param("contentType") Integer contentType, @Param("recordType") Integer recordType);

    /**
     * 检查记录是否存在
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @return 记录
     */
    WhRecord findExistRecord(@Param("userId") String userId, @Param("contentId") String contentId,
                            @Param("contentType") Integer contentType, @Param("recordType") Integer recordType);

    /**
     * 根据查询条件获取用户记录列表（带标题信息）
     * @param queryDTO 查询条件
     * @return 记录列表
     */
    List<WhRecordVO> getRecordListWithTitle(@Param("queryDTO") WhRecordQueryDTO queryDTO);
}
