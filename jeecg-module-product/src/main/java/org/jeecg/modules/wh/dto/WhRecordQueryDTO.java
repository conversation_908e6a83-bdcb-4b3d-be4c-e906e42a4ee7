package org.jeecg.modules.wh.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户门户记录查询DTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "WhRecordQueryDTO", description = "用户门户记录查询DTO")
public class WhRecordQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录
     */
    @ApiModelProperty(value = "记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录")
    private Integer recordType;

    /**
     * 内容类型：1.保险产品 2.保险资讯 3.保险公司
     */
    @ApiModelProperty(value = "内容类型：1.保险产品 2.保险资讯 3.保险公司")
    private Integer contentType;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
