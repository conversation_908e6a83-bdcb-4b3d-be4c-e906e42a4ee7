package org.jeecg.modules.wh.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.wh.entity.WhRecord;
import org.jeecg.modules.wh.dto.WhRecordQueryDTO;
import org.jeecg.modules.wh.vo.WhRecordVO;

import java.util.List;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
public interface IWhRecordService extends IService<WhRecord> {

    /**
     * 根据用户ID和记录类型查询记录
     * @param userId 用户ID
     * @param recordType 记录类型
     * @return 记录列表
     */
    List<WhRecord> findByUserIdAndRecordType(String userId, Integer recordType);

    /**
     * 根据用户ID查询所有记录
     * @param userId 用户ID
     * @return 记录列表
     */
    List<WhRecord> findByUserId(String userId);

    /**
     * 根据租户ID查询记录
     * @param tenantId 租户ID
     * @return 记录列表
     */
    List<WhRecord> findByTenantId(Integer tenantId);

    /**
     * 根据内容ID和内容类型查询记录
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 记录列表
     */
    List<WhRecord> findByContentIdAndContentType(String contentId, Integer contentType);

    /**
     * 添加或更新记录
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @param tenantId 租户ID
     * @return 操作结果
     */
    boolean addOrUpdateRecord(String userId, String contentId, Integer contentType, Integer recordType, Integer tenantId);

    /**
     * 删除用户指定类型的记录
     * @param userId 用户ID
     * @param recordType 记录类型
     * @return 删除数量
     */
    int deleteByUserIdAndRecordType(String userId, Integer recordType);

    /**
     * 删除用户指定内容的记录
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @return 删除数量
     */
    int deleteByUserIdAndContent(String userId, String contentId, Integer contentType, Integer recordType);

    /**
     * 检查记录是否存在
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @return 是否存在
     */
    boolean existsRecord(String userId, String contentId, Integer contentType, Integer recordType);

    /**
     * 获取用户浏览历史
     * @param userId 用户ID
     * @return 浏览历史列表
     */
    List<WhRecord> getUserBrowseHistory(String userId);

    /**
     * 获取用户收藏记录
     * @param userId 用户ID
     * @return 收藏记录列表
     */
    List<WhRecord> getUserFavorites(String userId);

    /**
     * 获取用户点赞记录
     * @param userId 用户ID
     * @return 点赞记录列表
     */
    List<WhRecord> getUserLikes(String userId);

    /**
     * 获取用户分享记录
     * @param userId 用户ID
     * @return 分享记录列表
     */
    List<WhRecord> getUserShares(String userId);

    /**
     * 根据查询条件获取用户记录列表（带标题信息）
     * @param queryDTO 查询条件
     * @return 记录列表
     */
    List<WhRecordVO> getRecordListWithTitle(WhRecordQueryDTO queryDTO);
}
