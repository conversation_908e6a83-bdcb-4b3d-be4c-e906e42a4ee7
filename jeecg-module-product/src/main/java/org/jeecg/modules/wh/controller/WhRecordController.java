package org.jeecg.modules.wh.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.wh.service.IWhRecordService;
import org.jeecg.modules.wh.dto.WhRecordQueryDTO;
import org.jeecg.modules.wh.dto.WhRecordSaveDTO;
import org.jeecg.modules.wh.vo.WhRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Api(tags="用户门户记录管理")
@RestController
@RequestMapping("/wh/record")
@Slf4j
public class WhRecordController {

    @Autowired
    private IWhRecordService whRecordService;

    /**
     * 保存用户记录
     *
     * @param saveDTO 保存参数
     * @return
     */
    @ApiOperation(value="保存用户记录", notes="保存用户记录")
    @PostMapping(value = "/saveRecord")
    public Result<String> saveRecord(@Valid @RequestBody WhRecordSaveDTO saveDTO) {
        try {
            boolean success = whRecordService.saveUserRecord(saveDTO);
            if (success) {
                return Result.OK("保存成功");
            } else {
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存用户记录失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 根据条件查询用户记录列表（带标题信息）
     *
     * @param queryDTO 查询条件
     * @return
     */
    @ApiOperation(value="根据条件查询用户记录列表", notes="根据条件查询用户记录列表（带标题信息）")
    @PostMapping(value = "/getRecordListWithTitle")
    public Result<List<WhRecordVO>> getRecordListWithTitle(@RequestBody WhRecordQueryDTO queryDTO) {
        try {
            List<WhRecordVO> list = whRecordService.getRecordListWithTitle(queryDTO);
            return Result.OK(list);
        } catch (Exception e) {
            log.error("查询用户记录列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
