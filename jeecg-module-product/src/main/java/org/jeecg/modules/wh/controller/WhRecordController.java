package org.jeecg.modules.wh.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.wh.entity.WhRecord;
import org.jeecg.modules.wh.service.IWhRecordService;
import org.jeecg.modules.wh.dto.WhRecordQueryDTO;
import org.jeecg.modules.wh.vo.WhRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Api(tags="用户门户记录管理")
@RestController
@RequestMapping("/wh/record")
@Slf4j
public class WhRecordController extends JeecgController<WhRecord, IWhRecordService> {

    @Autowired
    private IWhRecordService whRecordService;

    /**
     * 分页列表查询
     *
     * @param whRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value="用户门户记录-分页列表查询", notes="用户门户记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<WhRecord>> queryPageList(WhRecord whRecord,
                                                @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                HttpServletRequest req) {
        QueryWrapper<WhRecord> queryWrapper = QueryGenerator.initQueryWrapper(whRecord, req.getParameterMap());
        Page<WhRecord> page = new Page<WhRecord>(pageNo, pageSize);
        IPage<WhRecord> pageList = whRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param whRecord
     * @return
     */
    @ApiOperation(value="用户门户记录-添加", notes="用户门户记录-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody WhRecord whRecord) {
        whRecordService.save(whRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param whRecord
     * @return
     */
    @ApiOperation(value="用户门户记录-编辑", notes="用户门户记录-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
    public Result<String> edit(@RequestBody WhRecord whRecord) {
        whRecordService.updateById(whRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value="用户门户记录-通过id删除", notes="用户门户记录-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) String id) {
        whRecordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @ApiOperation(value="用户门户记录-批量删除", notes="用户门户记录-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.whRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value="用户门户记录-通过id查询", notes="用户门户记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<WhRecord> queryById(@RequestParam(name="id",required=true) String id) {
        WhRecord whRecord = whRecordService.getById(id);
        if(whRecord==null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(whRecord);
    }

    /**
     * 添加或更新用户记录
     *
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @param tenantId 租户ID
     * @return
     */
    @ApiOperation(value="添加或更新用户记录", notes="添加或更新用户记录")
    @PostMapping(value = "/addOrUpdateRecord")
    public Result<String> addOrUpdateRecord(@RequestParam String userId,
                                           @RequestParam String contentId,
                                           @RequestParam Integer contentType,
                                           @RequestParam Integer recordType,
                                           @RequestParam Integer tenantId) {
        try {
            boolean success = whRecordService.addOrUpdateRecord(userId, contentId, contentType, recordType, tenantId);
            if (success) {
                return Result.OK("操作成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("添加或更新用户记录失败", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户浏览历史
     *
     * @param userId 用户ID
     * @return
     */
    @ApiOperation(value="获取用户浏览历史", notes="获取用户浏览历史")
    @GetMapping(value = "/getUserBrowseHistory")
    public Result<List<WhRecord>> getUserBrowseHistory(@RequestParam String userId) {
        try {
            List<WhRecord> list = whRecordService.getUserBrowseHistory(userId);
            return Result.OK(list);
        } catch (Exception e) {
            log.error("获取用户浏览历史失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户收藏记录
     *
     * @param userId 用户ID
     * @return
     */
    @ApiOperation(value="获取用户收藏记录", notes="获取用户收藏记录")
    @GetMapping(value = "/getUserFavorites")
    public Result<List<WhRecord>> getUserFavorites(@RequestParam String userId) {
        try {
            List<WhRecord> list = whRecordService.getUserFavorites(userId);
            return Result.OK(list);
        } catch (Exception e) {
            log.error("获取用户收藏记录失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户点赞记录
     *
     * @param userId 用户ID
     * @return
     */
    @ApiOperation(value="获取用户点赞记录", notes="获取用户点赞记录")
    @GetMapping(value = "/getUserLikes")
    public Result<List<WhRecord>> getUserLikes(@RequestParam String userId) {
        try {
            List<WhRecord> list = whRecordService.getUserLikes(userId);
            return Result.OK(list);
        } catch (Exception e) {
            log.error("获取用户点赞记录失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户指定内容的记录
     *
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @return
     */
    @ApiOperation(value="删除用户指定内容的记录", notes="删除用户指定内容的记录")
    @DeleteMapping(value = "/deleteUserRecord")
    public Result<String> deleteUserRecord(@RequestParam String userId,
                                          @RequestParam String contentId,
                                          @RequestParam Integer contentType,
                                          @RequestParam Integer recordType) {
        try {
            int count = whRecordService.deleteByUserIdAndContent(userId, contentId, contentType, recordType);
            return Result.OK("删除成功，共删除" + count + "条记录");
        } catch (Exception e) {
            log.error("删除用户记录失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据条件查询用户记录列表（带标题信息）
     *
     * @param queryDTO 查询条件
     * @return
     */
    @ApiOperation(value="根据条件查询用户记录列表", notes="根据条件查询用户记录列表（带标题信息）")
    @PostMapping(value = "/getRecordListWithTitle")
    public Result<List<WhRecordVO>> getRecordListWithTitle(@RequestBody WhRecordQueryDTO queryDTO) {
        try {
            List<WhRecordVO> list = whRecordService.getRecordListWithTitle(queryDTO);
            return Result.OK(list);
        } catch (Exception e) {
            log.error("查询用户记录列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
