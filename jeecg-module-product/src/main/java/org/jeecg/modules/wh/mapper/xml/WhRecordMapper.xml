<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.wh.mapper.WhRecordMapper">

    <!-- 根据用户ID和记录类型查询记录 -->
    <select id="findByUserIdAndRecordType" resultType="org.jeecg.modules.wh.entity.WhRecord">
        SELECT * FROM wh_record 
        WHERE user_id = #{userId} AND record_type = #{recordType}
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID查询所有记录 -->
    <select id="findByUserId" resultType="org.jeecg.modules.wh.entity.WhRecord">
        SELECT * FROM wh_record 
        WHERE user_id = #{userId}
        ORDER BY record_type, create_time DESC
    </select>

    <!-- 根据租户ID查询记录 -->
    <select id="findByTenantId" resultType="org.jeecg.modules.wh.entity.WhRecord">
        SELECT * FROM wh_record 
        WHERE tenant_id = #{tenantId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据内容ID和内容类型查询记录 -->
    <select id="findByContentIdAndContentType" resultType="org.jeecg.modules.wh.entity.WhRecord">
        SELECT * FROM wh_record 
        WHERE content_id = #{contentId} AND content_type = #{contentType}
        ORDER BY create_time DESC
    </select>

    <!-- 删除用户指定类型的记录 -->
    <delete id="deleteByUserIdAndRecordType">
        DELETE FROM wh_record 
        WHERE user_id = #{userId} AND record_type = #{recordType}
    </delete>

    <!-- 删除用户指定内容的记录 -->
    <delete id="deleteByUserIdAndContent">
        DELETE FROM wh_record 
        WHERE user_id = #{userId} AND content_id = #{contentId} 
        AND content_type = #{contentType} AND record_type = #{recordType}
    </delete>

    <!-- 检查记录是否存在 -->
    <select id="findExistRecord" resultType="org.jeecg.modules.wh.entity.WhRecord">
        SELECT * FROM wh_record
        WHERE user_id = #{userId} AND content_id = #{contentId}
        AND content_type = #{contentType} AND record_type = #{recordType}
        LIMIT 1
    </select>

    <!-- 根据查询条件获取用户记录列表（带标题信息） -->
    <select id="getRecordListWithTitle" resultType="org.jeecg.modules.wh.vo.WhRecordVO">
        SELECT
            wr.content_id,
            wr.content_type,
            wr.create_time,
            CASE
                WHEN wr.content_type = 1 THEN wp.name
                WHEN wr.content_type = 2 THEN an.name
                WHEN wr.content_type = 3 THEN st.name
                ELSE ''
            END AS title
        FROM wh_record wr
        LEFT JOIN wh_products wp ON wr.content_type = 1 AND wr.content_id = wp.id
        LEFT JOIN app_news an ON wr.content_type = 2 AND wr.content_id = an.id
        LEFT JOIN sys_tenant st ON wr.content_type = 3 AND wr.content_id = CAST(st.id AS CHAR)
        WHERE 1=1
        <if test="queryDTO.userId != null and queryDTO.userId != ''">
            AND wr.user_id = #{queryDTO.userId}
        </if>
        <if test="queryDTO.recordType != null">
            AND wr.record_type = #{queryDTO.recordType}
        </if>
        <if test="queryDTO.contentType != null">
            AND wr.content_type = #{queryDTO.contentType}
        </if>
        <if test="queryDTO.startTime != null">
            AND wr.create_time >= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
            AND wr.create_time &lt;= #{queryDTO.endTime}
        </if>
        ORDER BY wr.create_time DESC
    </select>

</mapper>
