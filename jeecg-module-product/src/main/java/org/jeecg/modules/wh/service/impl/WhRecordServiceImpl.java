package org.jeecg.modules.wh.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.wh.entity.WhRecord;
import org.jeecg.modules.wh.mapper.WhRecordMapper;
import org.jeecg.modules.wh.service.IWhRecordService;
import org.jeecg.modules.wh.dto.WhRecordQueryDTO;
import org.jeecg.modules.wh.vo.WhRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class WhRecordServiceImpl extends ServiceImpl<WhRecordMapper, WhRecord> implements IWhRecordService {

    @Autowired
    private WhRecordMapper whRecordMapper;

    // 记录类型常量
    public static final int RECORD_TYPE_BROWSE = 1;  // 浏览记录
    public static final int RECORD_TYPE_FAVORITE = 2; // 收藏记录
    public static final int RECORD_TYPE_LIKE = 3;     // 点赞记录
    public static final int RECORD_TYPE_SHARE = 4;    // 分享记录

    @Override
    public List<WhRecord> findByUserIdAndRecordType(String userId, Integer recordType) {
        return whRecordMapper.findByUserIdAndRecordType(userId, recordType);
    }

    @Override
    public List<WhRecord> findByUserId(String userId) {
        return whRecordMapper.findByUserId(userId);
    }

    @Override
    public List<WhRecord> findByTenantId(Integer tenantId) {
        return whRecordMapper.findByTenantId(tenantId);
    }

    @Override
    public List<WhRecord> findByContentIdAndContentType(String contentId, Integer contentType) {
        return whRecordMapper.findByContentIdAndContentType(contentId, contentType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addOrUpdateRecord(String userId, String contentId, Integer contentType, Integer recordType, Integer tenantId) {
        try {
            // 检查记录是否已存在
            WhRecord existRecord = whRecordMapper.findExistRecord(userId, contentId, contentType, recordType);
            
            if (existRecord != null) {
                // 更新现有记录的时间
                existRecord.setUpdateTime(new Date());
                this.updateById(existRecord);
                log.info("更新用户记录成功，用户ID：{}，内容ID：{}，记录类型：{}", userId, contentId, recordType);
            } else {
                // 创建新记录
                WhRecord record = new WhRecord();
                record.setUserId(userId);
                record.setContentId(contentId);
                record.setContentType(contentType);
                record.setRecordType(recordType);
                record.setTenantId(tenantId);
                record.setCreateTime(new Date());
                record.setUpdateTime(new Date());
                this.save(record);
                log.info("添加用户记录成功，用户ID：{}，内容ID：{}，记录类型：{}", userId, contentId, recordType);
            }
            return true;
        } catch (Exception e) {
            log.error("添加或更新用户记录失败，用户ID：{}，内容ID：{}，记录类型：{}", userId, contentId, recordType, e);
            throw new RuntimeException("添加或更新用户记录失败", e);
        }
    }

    @Override
    public int deleteByUserIdAndRecordType(String userId, Integer recordType) {
        return whRecordMapper.deleteByUserIdAndRecordType(userId, recordType);
    }

    @Override
    public int deleteByUserIdAndContent(String userId, String contentId, Integer contentType, Integer recordType) {
        return whRecordMapper.deleteByUserIdAndContent(userId, contentId, contentType, recordType);
    }

    @Override
    public boolean existsRecord(String userId, String contentId, Integer contentType, Integer recordType) {
        WhRecord record = whRecordMapper.findExistRecord(userId, contentId, contentType, recordType);
        return record != null;
    }

    @Override
    public List<WhRecord> getUserBrowseHistory(String userId) {
        return this.findByUserIdAndRecordType(userId, RECORD_TYPE_BROWSE);
    }

    @Override
    public List<WhRecord> getUserFavorites(String userId) {
        return this.findByUserIdAndRecordType(userId, RECORD_TYPE_FAVORITE);
    }

    @Override
    public List<WhRecord> getUserLikes(String userId) {
        return this.findByUserIdAndRecordType(userId, RECORD_TYPE_LIKE);
    }

    @Override
    public List<WhRecord> getUserShares(String userId) {
        return this.findByUserIdAndRecordType(userId, RECORD_TYPE_SHARE);
    }

    @Override
    public List<WhRecordVO> getRecordListWithTitle(WhRecordQueryDTO queryDTO) {
        try {
            return whRecordMapper.getRecordListWithTitle(queryDTO);
        } catch (Exception e) {
            log.error("查询用户记录列表失败，查询条件：{}", queryDTO, e);
            throw new RuntimeException("查询用户记录列表失败", e);
        }
    }
}
