-- 用户门户记录统计表
CREATE TABLE `wh_record_statistics` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `count` INT(11) NOT NULL DEFAULT 0 COMMENT '数量',
    `record_type` TINYINT(1) NOT NULL COMMENT '记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录',
    `pid` VARCHAR(50) NOT NULL COMMENT '对应的内容ID',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_record_type_pid` (`record_type`, `pid`) USING BTREE COMMENT '记录类型和内容ID唯一索引',
    KEY `idx_record_type` (`record_type`) USING BTREE COMMENT '记录类型索引',
    KEY `idx_pid` (`pid`) USING BTREE COMMENT '内容ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户门户记录统计表';
